Rails.application.routes.draw do
  # Devise routes (public for registration/login)
  devise_for :users, controllers: {registrations: "users/registrations"}

  # Admin namespace (requires authentication)
  authenticated :user, lambda { |u| u.admin? } do
    namespace :admin do
      root "dashboard#index"

      resources :sales do
        member do
          post :approve
          post :reject
        end
      end

      resources :users do
        member do
          post :activate
          post :deactivate
        end
      end

      resources :stores do
        member do
          post :approve
          post :activate
          post :deactivate
        end
      end
    end
  end

  # Root path: authenticated users to dashboard, unauthenticated to landing
  authenticated :user do
    root to: "dashboard#show", as: :authenticated_root
  end

  # Store search and selection (public for registration flow)
  get "stores/search", to: "stores#search", as: :search_stores
  post "stores/select/:id", to: "stores#select", as: :select_store
  post "stores/create_store", to: "stores#create_store", as: :create_store_stores

  # Authenticated routes
  resources :products, only: [:index, :show]
  resources :orders, only: [:index, :new, :create]
  resource :cart, only: [:show, :destroy]
  resources :line_items, only: [:create, :update, :destroy]
  resources :sales, only: [:new, :create] do
    collection do
      get :points
    end
  end

  # Health check (public)
  get "up", to: "rails/health#show", as: :rails_health_check

  # PWA manifest and service worker (public)
  get "manifest", to: "rails/pwa#manifest", as: :pwa_manifest
  get "service-worker", to: "rails/pwa#service_worker", as: :pwa_service_worker

  root to: "landing#index"
end
