class StoresController < ApplicationController
  def search
    # Extract the actual search query for display in the form
    @query = if params[:q].is_a?(String)
      params[:q]
    elsif params[:q].present?
      params.dig(:q, :name_cont) || ""
    else
      ""
    end

    # Handle both simple search and advanced Ransack parameters
    search_params = if params[:q].is_a?(String)
      # Simple search - search across name and associated address fields
      {
        name_or_address_city_or_address_street_cont: params[:q]
      }
    else
      # Advanced Ransack search parameters
      params[:q] || {}
    end

    @q = Store.includes(:address, address: :state).ransack(search_params)
    @stores = @q.result(distinct: true).where(status: [:active, :requested]).limit(10)
  end

  def select
    store = Store.find(params[:id])
    session[:selected_store_id] = store.id
    redirect_to new_user_registration_path
  end

  def create_store
    store = Store.new(store_params.merge(status: :requested))
    if store.save
      address = store.build_address(address_params)
      if address.save
        session[:selected_store_id] = store.id
        redirect_to new_user_registration_path, notice: "Store requested. Continue registration."
      else
        store.destroy
        redirect_to search_stores_path, alert: address.errors.full_messages.to_sentence
      end
    else
      redirect_to search_stores_path, alert: store.errors.full_messages.to_sentence
    end
  end

  private

  def store_params
    params.require(:store).permit(:name, :phone_number, :brand_id)
  end

  def address_params
    params.require(:address).permit(:street, :city, :state_id, :postal_code, :country_id)
  end
end
