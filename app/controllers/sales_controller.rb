class SalesController < ApplicationController
  before_action :authenticate_user!

  def new
    @store = current_user.store
    if @store.nil? && !current_user.admin?
      flash[:alert] = "You must be assigned to a store to record a sale."
      redirect_to root_path and return
    end
    # Get the brand for the user's store (brand is now associated with store directly)
    @brand = @store&.brand || Brand.first # fallback if not associated
    @products = @brand.categories.flat_map(&:products)
    @sale = Sale.new

    respond_to do |format|
      format.html
      format.turbo_stream { render turbo_stream: turbo_stream.replace("modal", partial: "sales/form", locals: {sale: @sale, products: @products, brand: @brand}) }
    end
  end

  def create
    @store = current_user.store
    @brand = @store&.brand || Brand.first
    @products = @brand.categories.flat_map(&:products)
    @sale = Sale.new(sale_params)
    @sale.user = current_user
    # Set points from product data for user's country
    country = current_user.address&.country || Country.find_by(code: "CA")
    if @sale.product && country
      datum = @sale.product.product_country_data.find_by(country_id: country.id)
      @sale.points = datum&.points_earned
    end

    if @sale.save
      flash[:notice] = "Sale recorded successfully."
      redirect_to root_path
    else
      render :new, status: :unprocessable_entity
    end
  end

  # Returns points for a product and user's country as JSON
  def points
    product = Product.find_by(id: params[:product_id])
    country = current_user.address&.country || Country.find_by(code: "CA") # fallback to Canada
    points = if product && country
      datum = product.product_country_data.find_by(country_id: country.id)
      datum&.points_earned || "-"
    else
      "-"
    end
    render json: {points: points}
  end

  private

  def sale_params
    params.require(:sale).permit(:product_id, :serial_number, :sold_at, :notes, :points)
  end
end
