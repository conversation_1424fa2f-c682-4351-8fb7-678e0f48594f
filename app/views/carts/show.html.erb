<!-- Mobile-first PWA Cart -->
<div class="min-h-screen bg-gray-50">
  
  <%= render 'shared/top_navigation', 
      title: 'Shopping Cart', 
      subtitle: "Review your items",
      show_back_button: true,
      back_path: products_path %>

  <!-- Main Content -->
  <main class="pb-20">
    <% if @cart.line_items.any? %>
      
      <!-- Cart Items -->
      <section class="px-4 py-6">
        <div class="space-y-4">
          <% @cart.line_items.includes(:product).each do |item| %>
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div class="p-4">
                <div class="flex items-start space-x-4">
                  <!-- Product Image -->
                  <div class="w-16 h-16 bg-gray-100 rounded-lg flex-shrink-0 overflow-hidden">
                    <%= product_image_tag(item.product, class: "w-full h-full object-cover rounded-lg") %>
                  </div>
                  
                  <!-- Product Details -->
                  <div class="flex-1 min-w-0">
                    <h3 class="font-semibold text-gray-900 text-sm mb-1"><%= item.product.name %></h3>
                    <p class="text-xs text-gray-500 mb-2"><%= item.product.category.name %></p>
                    
                    <div class="flex items-center justify-between">
                      <div class="text-sm">
                        <span class="font-medium text-zeiss-600"><%= item.product.points_required %> pts</span>
                        <span class="text-gray-500">each</span>
                      </div>
                      
                      <!-- Quantity Controls -->
                      <div class="flex items-center space-x-2">
                        <%= form_with(model: item, url: line_item_path(item), method: :patch, local: true, class: "flex items-center space-x-2") do |f| %>
                          <%= f.number_field :quantity, 
                              min: 1, 
                              value: item.quantity, 
                              class: "w-16 text-center border border-gray-300 rounded-lg py-1 text-sm focus:ring-2 focus:ring-zeiss-500 focus:border-transparent" %>
                          <%= f.submit 'Update', 
                              class: "px-3 py-1 bg-zeiss-600 hover:bg-zeiss-700 text-white text-xs rounded-lg transition-colors" %>
                        <% end %>
                      </div>
                    </div>
                    
                    <div class="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
                      <div class="text-sm font-semibold text-gray-900">
                        Total: <span class="text-zeiss-600"><%= item.total_points %> pts</span>
                      </div>
                      
                      <%= button_to line_item_path(item), 
                          method: :delete, 
                          data: { turbo_confirm: 'Remove this item from your cart?' },
                          class: "inline-flex items-center px-2 py-1 text-xs text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors" do %>
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Remove
                      <% end %>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </section>

      <!-- Cart Summary -->
      <section class="bg-white border-t border-gray-200 sticky bottom-16 z-10">
        <div class="px-4 py-6">
          <div class="flex items-center justify-between mb-4">
            <div>
              <h3 class="text-lg font-semibold text-gray-900">Cart Total</h3>
              <p class="text-sm text-gray-500"><%= pluralize(@cart.line_items.sum(:quantity), 'item') %></p>
            </div>
            <div class="text-right">
              <div class="text-2xl font-bold text-zeiss-600"><%= @cart.total_points %> pts</div>
              <div class="text-sm text-gray-500">Total Points</div>
            </div>
          </div>
          
          <div class="space-y-3">
            <%= link_to new_order_path, 
                class: "w-full flex items-center justify-center px-6 py-4 bg-zeiss-600 hover:bg-zeiss-700 text-white font-semibold rounded-xl transition-colors shadow-sm" do %>
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Proceed to Checkout
            <% end %>
            
            <%= link_to products_path, 
                class: "w-full flex items-center justify-center px-6 py-3 border-2 border-gray-300 text-gray-700 hover:bg-gray-50 font-medium rounded-xl transition-colors" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
              </svg>
              Continue Shopping
            <% end %>
          </div>
        </div>
      </section>

    <% else %>
      <!-- Empty Cart State -->
      <section class="px-4 py-12">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
          <svg class="mx-auto h-16 w-16 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"></path>
          </svg>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Your cart is empty</h3>
          <p class="text-gray-600 mb-6">
            Start shopping to add products to your cart and redeem your points!
          </p>
          <%= link_to products_path, 
              class: "inline-flex items-center px-6 py-3 bg-zeiss-600 hover:bg-zeiss-700 text-white font-semibold rounded-xl transition-colors shadow-sm" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
            </svg>
            Start Shopping
          <% end %>
        </div>
      </section>
    <% end %>
  </main>

  <%= render 'shared/bottom_navigation', active_tab: 'shop' %>
</div>
