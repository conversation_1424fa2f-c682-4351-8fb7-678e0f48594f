<!DOCTYPE html>
<html>
<head>
  <title>Barcode Controller Test</title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>
  <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
  <%= javascript_importmap_tags %>
</head>
<body class="bg-gray-100 p-8">
  <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
    <h1 class="text-2xl font-bold mb-6">Barcode Controller Test</h1>
    
    <div class="space-y-4">
      <div class="relative" style="position: relative;" data-controller="barcode">
        <label for="test-input" class="block text-sm font-medium text-gray-700 mb-2">
          Test Barcode Input
        </label>
        <input 
          type="text" 
          id="test-input"
          placeholder="Enter or scan barcode"
          class="w-full px-4 py-3 pr-12 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          data-barcode-target="input"
          data-barcode-readers-value='["code_128_reader", "ean_reader"]'
          data-barcode-frequency-value="10"
          data-barcode-half-sample-value="true"
          data-barcode-patch-size-value="medium"
        />
      </div>
      
      <div class="text-sm text-gray-600">
        <p>This page tests the barcode controller functionality.</p>
        <p>Check the browser console for any errors.</p>
        <p>The barcode scan button should appear automatically.</p>
      </div>
    </div>
  </div>
</body>
</html>
