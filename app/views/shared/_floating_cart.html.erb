<!-- Floating <PERSON><PERSON> (Mobile) -->
<% if user_signed_in? && current_cart.line_items.any? %>
  <div class="fixed bottom-20 right-4 z-30 md:hidden">
    <%= link_to cart_path(current_cart),
        class: "flex items-center space-x-2 bg-zeiss-600 hover:bg-zeiss-700 text-white px-4 py-3 rounded-full shadow-lg transition-all duration-200 transform hover:scale-105" do %>
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"></path>
      </svg>
      <span class="font-medium text-sm">
        <%= current_cart.line_items.sum(:quantity) %>
      </span>
    <% end %>
  </div>
<% end %>