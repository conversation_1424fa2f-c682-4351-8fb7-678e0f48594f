<!-- Mobile-first PWA Checkout -->
<div class="min-h-screen bg-gray-50">
  
  <%= render 'shared/top_navigation', 
      title: 'Checkout', 
      subtitle: "Review and place your order",
      show_back_button: true,
      back_path: cart_path(@cart) %>

  <!-- Main Content -->
  <main class="pb-20">
    
    <!-- Order Summary Section -->
    <section class="bg-white border-b border-gray-200">
      <div class="px-4 py-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold text-gray-900">Order Summary</h2>
          <span class="text-sm text-gray-500"><%= pluralize(@cart.line_items.sum(:quantity), 'item') %></span>
        </div>

        <!-- Order Items -->
        <div class="space-y-3">
          <% @cart.line_items.includes(:product).each do |item| %>
            <div class="flex items-center space-x-4 py-3 border-b border-gray-100 last:border-b-0">
              <!-- Product Image -->
              <div class="w-12 h-12 bg-gray-100 rounded-lg flex-shrink-0 overflow-hidden">
                <%= product_image_tag(item.product, class: "w-full h-full object-cover") %>
              </div>
              
              <!-- Product Details -->
              <div class="flex-1 min-w-0">
                <h3 class="font-medium text-gray-900 text-sm truncate"><%= item.product.name %></h3>
                <p class="text-xs text-gray-500"><%= item.product.category.name %></p>
                <div class="flex items-center space-x-2 mt-1">
                  <span class="text-xs text-gray-600">Qty: <%= item.quantity %></span>
                  <span class="text-xs text-gray-400">|</span>
                  <span class="text-xs font-medium text-zeiss-600"><%= item.product.points_required %> pts each</span>
                </div>
              </div>
              
              <!-- Item Total -->
              <div class="text-right">
                <div class="text-sm font-semibold text-gray-900"><%= item.total_points %> pts</div>
              </div>
            </div>
          <% end %>
        </div>

        <!-- Order Total -->
        <div class="mt-6 pt-4 border-t border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-gray-900">Total Points</h3>
              <p class="text-sm text-gray-500">Points will be deducted from your wallet</p>
            </div>
            <div class="text-right">
              <div class="text-2xl font-bold text-zeiss-600"><%= @cart.total_points %> pts</div>
            </div>
          </div>
        </div>

        <!-- Points Balance Check -->
        <div class="mt-4 p-3 bg-blue-50 rounded-lg">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-sm font-medium text-blue-900">Your Points Balance</h4>
              <p class="text-sm text-blue-700"><%= current_user.wallet.points %> points available</p>
            </div>
            <div>
              <% if current_user.wallet.points >= @cart.total_points %>
                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-800">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  Sufficient
                </span>
              <% else %>
                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-red-100 text-red-800">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                  </svg>
                  Insufficient
                </span>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Shipping Information -->
    <section class="bg-white border-b border-gray-200">
      <div class="px-4 py-6">
        <%= form_with model: @order, url: orders_path, local: true, class: "space-y-6" do |f| %>
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Shipping Information</h3>
            
            <!-- Shipping Type -->
            <div class="space-y-3">
              <label class="text-sm font-medium text-gray-700">Where should we ship your order?</label>
              
              <div class="space-y-3">
                <label class="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg hover:border-zeiss-300 cursor-pointer transition-colors">
                  <%= f.radio_button :shipping_type, 'store', 
                      class: "mt-1 text-zeiss-600 focus:ring-zeiss-500 border-gray-300",
                      checked: true %>
                  <div class="flex-1">
                    <div class="flex items-center">
                      <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                      </svg>
                      <span class="font-medium text-gray-900">Ship to My Store</span>
                      <span class="ml-2 px-2 py-0.5 bg-green-100 text-green-800 text-xs rounded-full">Recommended</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">
                      <%= current_user.store.name %><br>
                      <% if current_user.store.address %>
                        <%= current_user.store.address.street_address %>, 
                        <%= current_user.store.address.city %>, 
                        <%= current_user.store.address.state&.name %>
                      <% end %>
                    </p>
                  </div>
                </label>

                <label class="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg hover:border-zeiss-300 cursor-pointer transition-colors">
                  <%= f.radio_button :shipping_type, 'user', 
                      class: "mt-1 text-zeiss-600 focus:ring-zeiss-500 border-gray-300" %>
                  <div class="flex-1">
                    <div class="flex items-center">
                      <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                      </svg>
                      <span class="font-medium text-gray-900">Ship to My Address</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">Enter your personal shipping address below</p>
                  </div>
                </label>
              </div>
            </div>

            <!-- Shipping Address Field -->
            <div class="mt-4" id="shipping-address-field" style="display: none;">
              <%= f.label :shipping_address, 'Shipping Address', class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= f.text_area :shipping_address, 
                  rows: 3,
                  placeholder: "Enter your full shipping address...",
                  class: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-zeiss-500 focus:border-transparent resize-none" %>
              <p class="text-xs text-gray-500 mt-1">Include street address, city, state, and postal code</p>
            </div>
          </div>

          <!-- Order Actions -->
          <div class="pt-6 border-t border-gray-200">
            <% if current_user.wallet.points >= @cart.total_points %>
              <%= f.submit 'Place Order', 
                  class: "w-full flex items-center justify-center px-6 py-4 bg-zeiss-600 hover:bg-zeiss-700 text-white font-semibold rounded-xl transition-colors shadow-sm" %>
            <% else %>
              <button type="button" disabled 
                      class="w-full flex items-center justify-center px-6 py-4 bg-gray-300 text-gray-500 font-semibold rounded-xl cursor-not-allowed">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                Insufficient Points
              </button>
              
              <div class="mt-3 text-center">
                <%= link_to new_sale_path, 
                    class: "inline-flex items-center px-4 py-2 border border-zeiss-600 text-zeiss-600 hover:bg-zeiss-50 font-medium rounded-lg transition-colors" do %>
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  Earn More Points
                <% end %>
              </div>
            <% end %>

            <div class="mt-4 text-center">
              <%= link_to cart_path(@cart), 
                  class: "inline-flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors" do %>
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
                </svg>
                Back to Cart
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </section>
  </main>

  <%= render 'shared/bottom_navigation', active_tab: 'shop' %>
</div>

<!-- JavaScript for shipping address toggle -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const storeRadio = document.querySelector('input[value="store"]');
    const userRadio = document.querySelector('input[value="user"]');
    const addressField = document.getElementById('shipping-address-field');
    
    function toggleAddressField() {
      if (userRadio.checked) {
        addressField.style.display = 'block';
      } else {
        addressField.style.display = 'none';
      }
    }
    
    storeRadio.addEventListener('change', toggleAddressField);
    userRadio.addEventListener('change', toggleAddressField);
    
    // Initial state
    toggleAddressField();
  });
</script>
