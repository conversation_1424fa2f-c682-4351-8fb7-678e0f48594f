<!-- Mobile-first PWA Orders Page -->
<div class="min-h-screen bg-gray-50">
  
  <%= render 'shared/top_navigation', 
      title: 'My Orders', 
      subtitle: "Order history and status",
      show_back_button: true,
      back_path: root_path %>

  <!-- Main Content -->
  <main class="pb-20">
    
    <!-- Orders Summary Section -->
    <section class="bg-white border-b border-gray-200">
      <div class="px-4 py-6">
        <div class="grid grid-cols-3 gap-4 text-center">
          <div>
            <div class="text-2xl font-bold text-gray-900"><%= @orders.count %></div>
            <div class="text-sm text-gray-500">Total Orders</div>
          </div>
          <div>
            <div class="text-2xl font-bold text-green-600">
              <%= @orders.where(status: :approved).count %>
            </div>
            <div class="text-sm text-gray-500">Approved</div>
          </div>
          <div>
            <div class="text-2xl font-bold text-yellow-600">
              <%= @orders.where(status: :pending).count %>
            </div>
            <div class="text-sm text-gray-500">Pending</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Orders List -->
    <section class="px-4 py-6">
      <% if @orders.any? %>
        <div class="space-y-4">
          <% @orders.each do |order| %>
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <!-- Order Header -->
              <div class="px-4 py-4 border-b border-gray-100">
                <div class="flex items-center justify-between">
                  <div>
                    <h3 class="font-semibold text-gray-900">
                      Order #<%= order.id %>
                    </h3>
                    <p class="text-sm text-gray-500">
                      <%= order.created_at.strftime('%B %d, %Y at %I:%M %p') %>
                    </p>
                  </div>
                  <div class="text-right">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                 <%= order.status == 'approved' ? 'bg-green-100 text-green-800' : 
                                     order.status == 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                                     order.status == 'shipped' ? 'bg-blue-100 text-blue-800' :
                                     'bg-red-100 text-red-800' %>">
                      <%= order.status.humanize %>
                    </span>
                  </div>
                </div>
              </div>

              <!-- Order Details -->
              <div class="px-4 py-4">
                <div class="space-y-3">
                  <!-- Order Items -->
                  <% if order.line_items.any? %>
                    <div>
                      <h4 class="text-sm font-medium text-gray-700 mb-2">Items Ordered</h4>
                      <div class="space-y-2">
                        <% order.line_items.each do |item| %>
                          <div class="flex items-center justify-between">
                            <div class="flex-1">
                              <p class="text-sm font-medium text-gray-900">
                                <%= item.product.name %>
                              </p>
                              <p class="text-xs text-gray-500">
                                Quantity: <%= item.quantity %>
                              </p>
                            </div>
                            <div class="text-sm font-medium text-blue-600">
                              <%= item.points %> pts
                            </div>
                          </div>
                        <% end %>
                      </div>
                    </div>
                  <% else %>
                    <div class="text-sm text-gray-500 italic">
                      No items found for this order
                    </div>
                  <% end %>

                  <!-- Shipping Information -->
                  <div class="pt-3 border-t border-gray-100">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span class="font-medium text-gray-700">Shipping:</span>
                        <span class="text-gray-900 ml-1">
                          <%= order.shipping_type&.humanize || 'Standard' %>
                        </span>
                      </div>
                      <div>
                        <span class="font-medium text-gray-700">Total Points:</span>
                        <span class="text-blue-600 font-semibold ml-1">
                          <%= order.points %> pts
                        </span>
                      </div>
                    </div>
                    
                    <% if order.shipping_address.present? %>
                      <div class="mt-2">
                        <span class="font-medium text-gray-700 text-sm">Address:</span>
                        <p class="text-sm text-gray-600 mt-1">
                          <%= order.shipping_address %>
                        </p>
                      </div>
                    <% end %>
                  </div>

                  <!-- Order Status Details -->
                  <% if order.status == 'shipped' && order.tracking_number.present? %>
                    <div class="pt-3 border-t border-gray-100">
                      <div class="bg-blue-50 rounded-lg p-3">
                        <p class="text-sm font-medium text-blue-900">Tracking Information</p>
                        <p class="text-sm text-blue-700 mt-1">
                          Tracking #: <%= order.tracking_number %>
                        </p>
                      </div>
                    </div>
                  <% elsif order.status == 'pending' %>
                    <div class="pt-3 border-t border-gray-100">
                      <div class="bg-yellow-50 rounded-lg p-3">
                        <p class="text-sm font-medium text-yellow-900">Order Status</p>
                        <p class="text-sm text-yellow-700 mt-1">
                          Your order is being reviewed and will be processed soon.
                        </p>
                      </div>
                    </div>
                  <% elsif order.status == 'rejected' %>
                    <div class="pt-3 border-t border-gray-100">
                      <div class="bg-red-50 rounded-lg p-3">
                        <p class="text-sm font-medium text-red-900">Order Update</p>
                        <p class="text-sm text-red-700 mt-1">
                          This order could not be processed. Please contact support if you have questions.
                        </p>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>
        </div>


      <% else %>
        <!-- Empty State -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
          <svg class="mx-auto h-16 w-16 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
          </svg>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">No orders yet</h3>
          <p class="text-gray-600 mb-6">
            You haven't placed any orders yet. Start earning points by recording sales!
          </p>
          <%= link_to root_path, 
              class: "inline-flex items-center px-4 py-2 bg-zeiss-600 hover:bg-zeiss-700 
                     text-white font-medium rounded-lg transition-colors" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Record Your First Sale
          <% end %>
        </div>
      <% end %>
    </section>
  </main>

  <%= render 'shared/bottom_navigation', active_tab: 'orders' %>
</div>
