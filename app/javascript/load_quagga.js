// Load Quagga2 dynamically to handle UMD format
export function loadQuagga() {
  if (window.Quagga) return Promise.resolve(window.Quagga);

  return new Promise((resolve, reject) => {
    // Check if Quagga is already available globally (UMD format)
    if (window.Quagga) {
      resolve(window.Quagga);
      return;
    }

    // Load the script dynamically
    const script = document.createElement('script');
    script.src = 'https://unpkg.com/@ericblade/quagga2@1.2.6/dist/quagga.min.js';
    script.onload = () => {
      if (window.Quagga) {
        resolve(window.Quagga);
      } else {
        reject(new Error('Quagga2 failed to load'));
      }
    };
    script.onerror = () => reject(new Error('Failed to load Quagga2 script'));
    document.head.appendChild(script);
  });
}
