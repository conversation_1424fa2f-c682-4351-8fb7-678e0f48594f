import { Controller } from "@hotwired/stimulus"
import { loadQuagga } from "../load_quagga"


export default class extends Controller {
  static targets = ["input", "scanner", "preview", "scanButton"]
  static values = {
    readers: { type: Array, default: ["code_128_reader", "ean_reader", "ean_8_reader", "upc_reader"] },
    frequency: { type: Number, default: 10 },
    halfSample: { type: Boolean, default: true },
    patchSize: { type: String, default: "medium" }
  }


  connect() {
    this.isScanning = false
    this.resultCollector = null
    this.setupScanButton()
  }

  disconnect() {
    this.stopScanner()
  }

  setupScanButton() {
    // If a scanButtonTarget exists, wire it up, else create one for backward compatibility
    if (this.hasScanButtonTarget) {
      this.scanButtonTarget.onclick = () => this.startScanner()
      // Ensure parent is relative for absolute positioning
      this.inputTarget.parentNode.style.position = 'relative'
      return
    }
    // Fallback: create the button if not present (legacy support)
    const existingBtn = this.element.querySelector('.barcode-scan-btn')
    if (existingBtn) existingBtn.remove()

    const scanBtn = document.createElement('button')
    scanBtn.type = 'button'
    scanBtn.className = 'barcode-scan-btn absolute inset-y-0 right-0 flex items-center pr-3'
    scanBtn.setAttribute('data-barcode-btn', '')
    scanBtn.innerHTML = `
      <svg class="w-5 h-5 text-gray-400 hover:text-gray-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h2M4 4h5m3 0h6m-9 4h2m3 0h2M4 20h5m3 0h6m-9-4h2m3 0h2"></path>
      </svg>
    `
    scanBtn.title = "Scan barcode"
    scanBtn.onclick = () => this.startScanner()
    this.inputTarget.parentNode.style.position = 'relative'
    this.inputTarget.parentNode.appendChild(scanBtn)
  }

  async startScanner() {
    if (this.isScanning) return

    try {
      this.isScanning = true

      // Check camera permissions first
      if (!await this.checkCameraPermissions()) {
        this.showError("Camera access denied. Please enable camera permissions and try again.")
        return
      }

      // Load Quagga dynamically
      const Quagga = await loadQuagga()
      
      this.createScannerOverlay()
      this.setupResultCollector(Quagga)
      await this.initializeQuagga(Quagga)

    } catch (error) {
      console.error("Scanner initialization error:", error)
      this.showError("Failed to initialize camera: " + error.message)
      this.stopScanner()
    }
  }

  async checkCameraPermissions() {
    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error("Camera not supported")
      }

      // Test camera access
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: "environment" }
      })
      stream.getTracks().forEach(track => track.stop())
      return true
    } catch (error) {
      return false
    }
  }

  createScannerOverlay() {
    this.overlay = document.createElement('div')
    this.overlay.className = 'fixed inset-0 bg-black bg-opacity-80 z-50 flex flex-col'

    this.overlay.innerHTML = `
      <div class="flex-shrink-0 bg-white px-4 py-3 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">Scan Barcode</h3>
        <button type="button" class="close-scanner p-2 rounded-full hover:bg-gray-100 transition-colors">
          <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div class="flex-1 relative">
        <div id="barcode-scanner" class="w-full h-full bg-black"></div>

        <!-- Scanning overlay with targeting box -->
        <div class="absolute inset-0 flex items-center justify-center">
          <div class="w-64 h-32 border-2 border-white border-dashed rounded-lg bg-transparent relative">
            <div class="absolute -top-2 -left-2 w-4 h-4 border-t-2 border-l-2 border-white"></div>
            <div class="absolute -top-2 -right-2 w-4 h-4 border-t-2 border-r-2 border-white"></div>
            <div class="absolute -bottom-2 -left-2 w-4 h-4 border-b-2 border-l-2 border-white"></div>
            <div class="absolute -bottom-2 -right-2 w-4 h-4 border-b-2 border-r-2 border-white"></div>
          </div>
        </div>

        <!-- Status indicator -->
        <div class="absolute top-4 left-4 right-4">
          <div class="bg-black bg-opacity-50 text-white px-3 py-2 rounded-lg text-sm text-center">
            <span class="scanning-status">Position barcode within the frame</span>
          </div>
        </div>
      </div>

      <div class="flex-shrink-0 bg-white px-4 py-3">
        <div class="flex items-center justify-between">
          <button type="button" class="toggle-torch px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium transition-colors">
            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
            Flash
          </button>
          <button type="button" class="manual-entry px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors">
            Enter Manually
          </button>
        </div>
      </div>
    `

    document.body.appendChild(this.overlay)

    // Add event listeners
    this.overlay.querySelector('.close-scanner').onclick = () => this.stopScanner()
    this.overlay.querySelector('.toggle-torch').onclick = () => this.toggleTorch()
    this.overlay.querySelector('.manual-entry').onclick = () => this.stopScanner()

    // Prevent body scroll
    document.body.style.overflow = 'hidden'
  }

  setupResultCollector(Quagga) {
    this.resultCollector = Quagga.ResultCollector.create({
      capture: false,
      capacity: 20,
      blacklist: [],
      filter: (codeResult) => {
        // Only accept results with good confidence
        return codeResult.code && codeResult.code.length >= 8
      }
    })
    Quagga.registerResultCollector(this.resultCollector)
  }

  async initializeQuagga(Quagga) {
    return new Promise((resolve, reject) => {
      const config = {
        inputStream: {
          name: "Live",
          type: "LiveStream",
          target: document.getElementById('barcode-scanner'),
          constraints: {
            width: { min: 640, ideal: 1280, max: 1920 },
            height: { min: 480, ideal: 720, max: 1080 },
            facingMode: "environment",
            aspectRatio: { ideal: 16/9 }
          },
          area: {
            top: "20%",
            right: "10%",
            left: "10%",
            bottom: "20%"
          },
          singleChannel: false
        },
        frequency: this.frequencyValue,
        locator: {
          halfSample: this.halfSampleValue,
          patchSize: this.patchSizeValue,
          debug: {
            showCanvas: false,
            showPatches: false,
            showFoundPatches: false,
            showSkeleton: false,
            showLabels: false,
            showPatchLabels: false,
            showRemainingPatchLabels: false,
            boxFromPatches: {
              showTransformed: false,
              showTransformedBox: false,
              showBB: false
            }
          }
        },
        decoder: {
          readers: this.readersValue,
          debug: {
            drawBoundingBox: false,
            showFrequency: false,
            drawScanline: false,
            showPattern: false
          },
          multiple: false
        },
        locate: true,
        debug: false
      }

      Quagga.init(config, (err) => {
        if (err) {
          reject(err)
          return
        }

        Quagga.start()
        this.setupQuaggaEvents(Quagga)
        resolve()
      })
    })
  }

  setupQuaggaEvents(Quagga) {
    Quagga.onDetected((data) => {
      if (data && data.codeResult && data.codeResult.code) {
        // Get multiple results to verify
        const results = this.resultCollector.getResults()
        const recentResults = results.slice(-5) // Last 5 results

        // Check for consistency
        const codes = recentResults.map(r => r.codeResult.code)
        const mostCommon = this.getMostCommonCode(codes)

        if (mostCommon && this.isValidBarcode(mostCommon)) {
          this.handleSuccessfulScan(mostCommon)
        }
      }
    })

    // Handle processing updates
    Quagga.onProcessed((result) => {
      this.updateScanningStatus(result)
    })
  }

  getMostCommonCode(codes) {
    const frequency = {}
    codes.forEach(code => {
      frequency[code] = (frequency[code] || 0) + 1
    })

    let maxCount = 0
    let mostCommon = null

    Object.entries(frequency).forEach(([code, count]) => {
      if (count > maxCount && count >= 2) { // Require at least 2 occurrences
        maxCount = count
        mostCommon = code
      }
    })

    return mostCommon
  }

  isValidBarcode(code) {
    // Basic validation - adjust based on your requirements
    return code &&
           code.length >= 8 &&
           code.length <= 18 &&
           /^[0-9A-Z\-]+$/.test(code)
  }

  handleSuccessfulScan(code) {
    // Visual feedback
    this.showSuccessAnimation()

    // Update input
    this.inputTarget.value = code
    this.inputTarget.dispatchEvent(new Event('input', { bubbles: true }))
    this.inputTarget.dispatchEvent(new Event('change', { bubbles: true }))

    // Stop scanner after short delay
    setTimeout(() => {
      this.stopScanner()
    }, 500)
  }

  showSuccessAnimation() {
    const statusEl = this.overlay?.querySelector('.scanning-status')
    if (statusEl) {
      statusEl.textContent = "✓ Barcode detected!"
      statusEl.parentElement.className = statusEl.parentElement.className.replace('bg-black', 'bg-green-600')
    }
  }

  updateScanningStatus(result) {
    const statusEl = this.overlay?.querySelector('.scanning-status')
    if (!statusEl) return

    if (result && result.codeResult) {
      statusEl.textContent = "Reading barcode..."
    } else {
      statusEl.textContent = "Position barcode within the frame"
    }
  }

  async toggleTorch() {
    try {
      if (!window.Quagga) return
      const track = window.Quagga.CameraAccess.getActiveTrack()

      if (track && track.getCapabilities && track.getCapabilities().torch) {
        const constraints = track.getConstraints()
        const newTorchState = !constraints.torch

        await track.applyConstraints({
          advanced: [{ torch: newTorchState }]
        })

        // Update button state
        const torchBtn = this.overlay?.querySelector('.toggle-torch')
        if (torchBtn) {
          torchBtn.classList.toggle('bg-yellow-200', newTorchState)
          torchBtn.classList.toggle('bg-gray-100', !newTorchState)
        }
      }
    } catch (error) {
      console.warn("Torch not supported:", error)
    }
  }

  stopScanner() {
    if (!this.isScanning) return

    this.isScanning = false

    // Stop Quagga
    if (window.Quagga) {
      window.Quagga.stop()
      window.Quagga.offDetected()
      window.Quagga.offProcessed()
    }

    // Remove overlay
    if (this.overlay) {
      document.body.removeChild(this.overlay)
      this.overlay = null
    }

    // Restore body scroll
    document.body.style.overflow = ''
  }

  showError(message) {
    // Create a simple error toast
    const toast = document.createElement('div')
    toast.className = 'fixed top-4 left-4 right-4 bg-red-600 text-white px-4 py-3 rounded-lg z-50 text-sm'
    toast.textContent = message

    document.body.appendChild(toast)

    setTimeout(() => {
      if (toast.parentNode) {
        document.body.removeChild(toast)
      }
    }, 5000)
  }
}