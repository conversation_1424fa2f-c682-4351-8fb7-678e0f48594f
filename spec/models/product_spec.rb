# == Schema Information
#
# Table name: products
#
#  id          :bigint           not null, primary key
#  description :text
#  name        :string           not null
#  sku         :string           not null
#  status      :string           not null
#  upc         :string           not null
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  category_id :bigint           not null
#
# Indexes
#
#  index_products_on_category_id  (category_id)
#  index_products_on_sku          (sku) UNIQUE
#  index_products_on_upc          (upc) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (category_id => categories.id)
#
require "rails_helper"

RSpec.describe Product, type: :model do
  subject(:product) { build(:product) }

  describe "validations" do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:sku) }
    it { is_expected.to validate_presence_of(:upc) }
    it { is_expected.to validate_presence_of(:status) }
    it { is_expected.to validate_uniqueness_of(:sku) }
    it { is_expected.to validate_uniqueness_of(:upc).case_insensitive }

    context "EAN-13 upc validation" do
      it "is valid with a correct EAN-13 upc" do
        product.upc = "4006381333931" # valid EAN-13
        expect(product).to be_valid
      end

      it "is invalid if upc is not 13 digits" do
        product.upc = "123456789012" # 12 digits
        expect(product).not_to be_valid
        expect(product.errors[:upc]).to include("must be 13 digits (EAN-13)")
      end

      it "is invalid if upc has non-digit characters" do
        product.upc = "40063813339a1"
        expect(product).not_to be_valid
        expect(product.errors[:upc]).to include("must be 13 digits (EAN-13)")
      end

      it "is invalid if upc has wrong checksum" do
        product.upc = "4006381333932" # invalid checksum
        expect(product).not_to be_valid
        expect(product.errors[:upc]).to include("is not a valid EAN-13 barcode (invalid checksum)")
      end
    end
  end

  describe "barcode" do
    it "responds to barcode_svg" do
      expect(product).to respond_to(:barcode)
    end
  end

  describe "#country_data" do
    let(:product) { FactoryBot.create(:product) }
    let(:ca_country) { FactoryBot.create(:country, code: "CA", name: "Canada") }
    let(:us_country) { FactoryBot.create(:country, code: "US", name: "United States") }
    let!(:ca_data) { FactoryBot.create(:product_country_datum, product: product, country: ca_country, msrp: 10.99, points_earned: 100, points_cost: 50) }
    let!(:us_data) { FactoryBot.create(:product_country_datum, product: product, country: us_country, msrp: 12.99, points_earned: 120, points_cost: 60) }

    it "returns the correct ProductCountryDatum for CA" do
      expect(product.country_data("CA")).to eq(ca_data)
    end

    it "returns the correct ProductCountryDatum for US" do
      expect(product.country_data("US")).to eq(us_data)
    end

    it "returns the US ProductCountryDatum for unsupported country (fallback)" do
      expect(product.country_data("MX")).to eq(us_data)
    end
  end

  describe "associations" do
    it { is_expected.to have_many(:product_country_data).dependent(:destroy) }
  end
end
