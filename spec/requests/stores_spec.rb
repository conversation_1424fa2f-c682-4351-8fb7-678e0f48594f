require "rails_helper"

RSpec.describe "Stores", type: :request do
  let(:user) { create(:user) }
  let(:brand) { create(:brand) }
  let(:store) { create(:store, brand: brand) }

  before do
    login_as(user, scope: :user)
  end

  describe "GET /stores/search" do
    it "returns http success" do
      get search_stores_path
      expect(response).to have_http_status(:success)
    end

    it "searches stores by name" do
      store1 = create(:store, name: "Zeiss Optical", status: :active)
      store2 = create(:store, name: "Vision Center", status: :active)

      get search_stores_path, params: {q: {name_cont: "<PERSON>eiss"}}

      expect(assigns(:stores)).to include(store1)
      expect(assigns(:stores)).not_to include(store2)
      expect(assigns(:query)).to eq("Zeiss")
    end

    it "only shows active and requested stores" do
      active_store = create(:store, name: "Active Store", status: :active)
      requested_store = create(:store, name: "Requested Store", status: :requested)
      inactive_store = create(:store, name: "Inactive Store", status: :inactive)

      get search_stores_path, params: {q: "Store"}

      expect(assigns(:stores)).to include(active_store, requested_store)
      expect(assigns(:stores)).not_to include(inactive_store)
    end

    it "limits results to 10 stores" do
      15.times { |i| create(:store, name: "Store #{i}", status: :active) }

      get search_stores_path, params: {q: "Store"}

      expect(assigns(:stores).count).to eq(10)
    end

    it "extracts query from string parameter" do
      get search_stores_path, params: {q: "test query"}
      expect(assigns(:query)).to eq("test query")
    end
  end

  describe "POST /stores/:id/select" do
    it "redirects after selecting a store" do
      post select_store_path(store)
      expect(response).to be_redirect
    end
  end

  describe "POST /stores/create_store" do
    it "creates a new store and redirects" do
      region = create(:region)
      state = create(:state, region: region)
      country = create(:country, code: "CA", name: "Canada")
      expect {
        post create_store_stores_path, params: {
          store: {name: "Test Store", phone_number: "1234567890", status: "active", brand_id: brand.id},
          address: {street: "123 Main", city: "Testville", postal_code: "12345", country_id: country.id, state_id: state.id}
        }
      }.to change(Store, :count).by(1)
      expect(response).to be_redirect
    end
  end
end
